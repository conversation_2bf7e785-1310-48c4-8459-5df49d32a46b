

import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from "axios";
import { apiCache } from '../utils/apiCache';

axios.defaults.withCredentials = true;
/**
 * Interface for JWT token payload
 */
interface JWTPayload {
    exp: number;
    iat?: number;
    sub?: string;
    [key: string]: any;
}

/**
 * Interface for token refresh response
 */
interface TokenRefreshResponse {
    access_token: string;
}

/**
 * Type for token refresh callback function
 */
type TokenRefreshCallback = (newAccessToken: string) => void;


/** API base URL from environment variables */
const API_ENDPOINT: string = import.meta.env.VITE_API_BASE_URL;



/**
 * Retrieves the access token from localStorage
 * @returns {string | null} The access token or null if not found
 */
const getTokens = (): string | null => {
    const token = localStorage.getItem("access_token");
    return token || null;
};

/**
 * Refreshes the access token using the refresh token
 * @returns {Promise<string>} Promise that resolves to the new access token
 */
const refreshAccessToken = async (): Promise<string> => {
    const response: AxiosResponse<TokenRefreshResponse> = await axios.post(
        `${API_ENDPOINT}/refresh`,
        null,
        { withCredentials: true}
    );
    console.log("🚀 ~ refreshAccessToken ~ response:", response)

    return response.data.access_token;
};

/** Flag to prevent multiple simultaneous refresh requests */
let isRefreshing: boolean = false;

/** Array to store callbacks waiting for token refresh */
let refreshSubscribers: TokenRefreshCallback[] = [];

/**
 * Subscribes a callback to be executed when token refresh completes
 * @param {TokenRefreshCallback} cb - Callback function to execute
 */
const subscribeTokenRefresh = (cb: TokenRefreshCallback): void => {
    refreshSubscribers.push(cb);
};

/**
 * Notifies all subscribers when a new token is received
 * @param {string} newAccessToken - The new access token
 */
const onRefreshed = (newAccessToken: string): void => {
    refreshSubscribers.forEach((cb) => cb(newAccessToken));
    refreshSubscribers = [];
};

/**
 * Configured axios instance with base URL and default headers
 */
export const api: AxiosInstance = axios.create({
    baseURL: API_ENDPOINT,
    withCredentials: true,
    headers: {
        "Content-Type": "application/json",
    },
    
});

/**
 * Request interceptor to add authorization header and implement caching
 */
api.interceptors.request.use(
    (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
        const token = getTokens();

        if (token && config.headers) {
            config.headers["Authorization"] = `Bearer ${token}`;
        }

        // Dynamically set Content-Type based on data type
        if (config.data instanceof FormData) {
            if (config.headers) {
                config.headers["Content-Type"] = "multipart/form-data";
            }
        } else if (config.data && config.headers) {
            config.headers["Content-Type"] = "application/json";
        }



        return config;
    },
    (error: AxiosError) => Promise.reject(error)
);


/**
 * Response interceptor to handle 401 errors, token refresh, and caching
 */
api.interceptors.response.use(
    (response: AxiosResponse) => {
        // Cache successful GET responses
        if (response.config.method === 'get' && response.status === 200) {
            const url = response.config.url || '';
            const params = response.config.params;
            apiCache.set(url, response.data, params);
        }
        return response;
    },
    async (error: AxiosError) => {
        const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

        // Handle 401 Unauthorized errors
        if (error.response?.data && typeof error.response.data === 'object' && 'code' in error.response.data && error.response.data.code === 401) {
            if (!isRefreshing) {
                console.log("chal rha h ")
                try {
                    const newAccessToken = await refreshAccessToken();
                    console.log("axios .interceptors.response.use: newAccessToken", newAccessToken);
                    localStorage.setItem("access_token", newAccessToken);
                    isRefreshing = false;
                    onRefreshed(newAccessToken);

                    // Retry the original request with new token
                    if (originalRequest.headers) {
                        originalRequest.headers["Authorization"] = `Bearer ${newAccessToken}`;
                    }
                    return api(originalRequest);
                } catch (refreshError) {
                    isRefreshing = false;
                    // localStorage.removeItem("access_token");
                    // window.location.href = "/auth";
                    return Promise.reject(refreshError);
                }
            } else {
                // If refresh is already in progress, wait for it
                return new Promise((resolve) => {
                    subscribeTokenRefresh((newAccessToken: string) => {
                        if (originalRequest.headers) {
                            originalRequest.headers["Authorization"] = `Bearer ${newAccessToken}`;
                        }
                        resolve(api(originalRequest));
                    });
                });
            }
        }

        return Promise.reject(error);
    }
);

export default api;

