import React, { useEffect, memo } from 'react';
// import { preloadCriticalImages, setupImageLazyLoading } from '../utils/imageOptimizer';
// import { initializeBundleOptimizations } from '../utils/bundleOptimizer';
import { preloadCriticalResources, prefetchResources } from '../utils/preloader';
import { measurePerformance } from '../utils/performance';

interface PerformanceOptimizerProps {
  children: React.ReactNode;
  enableServiceWorker?: boolean;
  enableImageOptimization?: boolean;
  enableBundleOptimization?: boolean;
  enablePreloading?: boolean;
}

/**
 * Performance Optimizer Component
 * Wraps the app with performance optimizations
 */
const PerformanceOptimizer: React.FC<PerformanceOptimizerProps> = memo(({
  children,
  enableServiceWorker = true,
  enableImageOptimization = true,
  enableBundleOptimization = true,
  enablePreloading = true
}) => {
  useEffect(() => {
    console.log('🚀 Performance Optimizer initialized');

    // Initialize performance monitoring
    try {
      measurePerformance();
    } catch (error) {
      console.warn('Performance monitoring failed:', error);
    }

    // Preload critical resources
    if (enablePreloading) {
      try {
        preloadCriticalResources();

        // Prefetch non-critical resources after initial load
        setTimeout(() => {
          prefetchResources();
        }, 1000);
      } catch (error) {
        console.warn('Preloading failed:', error);
      }
    }

    console.log('✅ Performance optimizations loaded successfully');

    // Simple cleanup for now
    return () => {
      console.log('🧹 Performance Optimizer cleanup');
    };
  }, [enableServiceWorker, enableImageOptimization, enableBundleOptimization, enablePreloading]);

  // Simplified for now - just log that we're working
  useEffect(() => {
    console.log('🎯 Performance hints would be added here');
  }, []);

  return (
    <>
      {/* Performance Optimizer is active */}
      {children}
    </>
  );
});

PerformanceOptimizer.displayName = 'PerformanceOptimizer';

/**
 * Performance monitoring hook
 */
export function usePerformanceMonitoring() {
  useEffect(() => {
    let startTime = performance.now();
    
    const logPerformance = () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      if (renderTime > 16) { // More than one frame at 60fps
        console.warn(`Slow render detected: ${renderTime.toFixed(2)}ms`);
      }
    };

    // Log performance after render
    const timeoutId = setTimeout(logPerformance, 0);
    
    return () => {
      clearTimeout(timeoutId);
    };
  });
}

/**
 * Component performance wrapper
 */
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const WrappedComponent = memo((props: P) => {
    usePerformanceMonitoring();
    return <Component {...props} />;
  });
  
  WrappedComponent.displayName = `withPerformanceMonitoring(${componentName || Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Performance-optimized error boundary
 */
export class PerformanceErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Performance Error Boundary caught an error:', error, errorInfo);
    
    // Log performance impact
    if ('performance' in window) {
      const entries = performance.getEntriesByType('measure');
      console.log('Performance entries at error:', entries);
    }
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      
      if (FallbackComponent) {
        return <FallbackComponent />;
      }
      
      return (
        <div className="flex items-center justify-center min-h-screen bg-black text-white">
          <div className="text-center">
            <h2 className="text-xl font-bold mb-4">Something went wrong</h2>
            <p className="text-gray-400 mb-4">The application encountered an error</p>
            <button
              type="button"
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-black rounded hover:bg-primary/80"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default PerformanceOptimizer;
