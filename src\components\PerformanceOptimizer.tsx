import React, { useEffect, memo } from 'react';
import { preloadCriticalImages, setupImageLazyLoading } from '../utils/imageOptimizer';
import { initializeBundleOptimizations } from '../utils/bundleOptimizer';
import { preloadCriticalResources, prefetchResources } from '../utils/preloader';
import { measurePerformance } from '../utils/performance';

interface PerformanceOptimizerProps {
  children: React.ReactNode;
  enableServiceWorker?: boolean;
  enableImageOptimization?: boolean;
  enableBundleOptimization?: boolean;
  enablePreloading?: boolean;
}

/**
 * Performance Optimizer Component
 * Wraps the app with performance optimizations
 */
const PerformanceOptimizer: React.FC<PerformanceOptimizerProps> = memo(({
  children,
  enableServiceWorker = true,
  enableImageOptimization = true,
  enableBundleOptimization = true,
  enablePreloading = true
}) => {
  useEffect(() => {
    let cleanupFunctions: (() => void)[] = [];

    // Initialize performance monitoring
    measurePerformance();

    // Preload critical resources
    if (enablePreloading) {
      preloadCriticalResources();
      
      // Prefetch non-critical resources after initial load
      setTimeout(() => {
        prefetchResources();
      }, 1000);
    }

    // Initialize image optimization
    if (enableImageOptimization) {
      // Preload critical images
      const criticalImages = [
        '/src/assets/images/logo-white.avif',
        '/src/assets/images/background.avif',
        '/src/assets/map/Lock.avif'
      ];
      
      preloadCriticalImages(criticalImages);
      
      // Setup lazy loading for existing images
      setupImageLazyLoading();
      
      // Setup lazy loading for dynamically added images
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              const lazyImages = element.querySelectorAll('img[data-src]');
              lazyImages.forEach((img) => {
                setupImageLazyLoading();
              });
            }
          });
        });
      });
      
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
      
      cleanupFunctions.push(() => observer.disconnect());
    }

    // Initialize bundle optimizations
    if (enableBundleOptimization) {
      const bundleCleanup = initializeBundleOptimizations();
      cleanupFunctions.push(bundleCleanup);
    }

    // Optimize scroll performance
    let ticking = false;
    const optimizedScrollHandler = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          // Dispatch optimized scroll event
          window.dispatchEvent(new CustomEvent('optimized-scroll', {
            detail: { scrollY: window.scrollY }
          }));
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', optimizedScrollHandler, { passive: true });
    cleanupFunctions.push(() => {
      window.removeEventListener('scroll', optimizedScrollHandler);
    });

    // Optimize touch events for mobile
    const optimizedTouchHandler = (e: TouchEvent) => {
      // Prevent default only when necessary
      if (e.touches.length > 1) {
        e.preventDefault();
      }
    };

    document.addEventListener('touchstart', optimizedTouchHandler, { passive: false });
    cleanupFunctions.push(() => {
      document.removeEventListener('touchstart', optimizedTouchHandler);
    });

    // Memory cleanup on visibility change
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // App is hidden, perform cleanup
        if ('gc' in window && typeof (window as any).gc === 'function') {
          (window as any).gc();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    cleanupFunctions.push(() => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    });

    // Cleanup function
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, [enableServiceWorker, enableImageOptimization, enableBundleOptimization, enablePreloading]);

  // Add performance hints to the DOM
  useEffect(() => {
    // Add resource hints
    const hints = [
      { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
      { rel: 'dns-prefetch', href: '//api.example.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: 'anonymous' }
    ];

    const linkElements: HTMLLinkElement[] = [];

    hints.forEach(hint => {
      const link = document.createElement('link');
      link.rel = hint.rel;
      link.href = hint.href;
      if ('crossorigin' in hint) {
        link.crossOrigin = hint.crossorigin;
      }
      document.head.appendChild(link);
      linkElements.push(link);
    });

    return () => {
      linkElements.forEach(link => {
        if (link.parentNode) {
          link.parentNode.removeChild(link);
        }
      });
    };
  }, []);

  return <>{children}</>;
});

PerformanceOptimizer.displayName = 'PerformanceOptimizer';

/**
 * Performance monitoring hook
 */
export function usePerformanceMonitoring() {
  useEffect(() => {
    let startTime = performance.now();
    
    const logPerformance = () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      if (renderTime > 16) { // More than one frame at 60fps
        console.warn(`Slow render detected: ${renderTime.toFixed(2)}ms`);
      }
    };

    // Log performance after render
    const timeoutId = setTimeout(logPerformance, 0);
    
    return () => {
      clearTimeout(timeoutId);
    };
  });
}

/**
 * Component performance wrapper
 */
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const WrappedComponent = memo((props: P) => {
    usePerformanceMonitoring();
    return <Component {...props} />;
  });
  
  WrappedComponent.displayName = `withPerformanceMonitoring(${componentName || Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Performance-optimized error boundary
 */
export class PerformanceErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Performance Error Boundary caught an error:', error, errorInfo);
    
    // Log performance impact
    if ('performance' in window) {
      const entries = performance.getEntriesByType('measure');
      console.log('Performance entries at error:', entries);
    }
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      
      if (FallbackComponent) {
        return <FallbackComponent />;
      }
      
      return (
        <div className="flex items-center justify-center min-h-screen bg-black text-white">
          <div className="text-center">
            <h2 className="text-xl font-bold mb-4">Something went wrong</h2>
            <p className="text-gray-400 mb-4">The application encountered an error</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-black rounded hover:bg-primary/80"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default PerformanceOptimizer;
