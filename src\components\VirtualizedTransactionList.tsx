import React, { memo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { formatDistanceToNow } from 'date-fns';

interface Transaction {
  id: string;
  timestamp: string;
  type: string;
  description: string;
  amount: number;
  balance: number;
  status: string;
}

interface TransactionRowProps {
  index: number;
  style: React.CSSProperties;
  data: Transaction[];
}

// Memoized transaction row component for better performance
const TransactionRow = memo<TransactionRowProps>(({ index, style, data }) => {
  const transaction = data[index];
  
  if (!transaction) {
    return (
      <div style={style} className="flex items-center px-4 py-3 border-b border-white/5">
        <div className="animate-pulse flex space-x-4 w-full">
          <div className="h-4 bg-white/10 rounded w-1/4"></div>
          <div className="h-4 bg-white/10 rounded w-1/6"></div>
          <div className="h-4 bg-white/10 rounded w-1/3"></div>
          <div className="h-4 bg-white/10 rounded w-1/6"></div>
          <div className="h-4 bg-white/10 rounded w-1/6"></div>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return 'text-green-400';
      case 'pending':
        return 'text-yellow-400';
      case 'failed':
      case 'error':
        return 'text-red-400';
      default:
        return 'text-white/60';
    }
  };

  const getAmountColor = (amount: number) => {
    return amount >= 0 ? 'text-green-400' : 'text-red-400';
  };

  return (
    <div style={style} className="flex items-center px-4 py-3 border-b border-white/5 hover:bg-white/5 transition-colors">
      <div className="grid grid-cols-6 gap-4 w-full text-sm">
        <div className="text-white/80">
          {formatDistanceToNow(new Date(transaction.timestamp), { addSuffix: true })}
        </div>
        <div className="text-white/60 capitalize">
          {transaction.type}
        </div>
        <div className="text-white/80 truncate" title={transaction.description}>
          {transaction.description}
        </div>
        <div className={`font-medium ${getAmountColor(transaction.amount)}`}>
          {transaction.amount >= 0 ? '+' : ''}{transaction.amount.toFixed(2)}
        </div>
        <div className="text-white/60">
          {transaction.balance.toFixed(2)}
        </div>
        <div className={`capitalize ${getStatusColor(transaction.status)}`}>
          {transaction.status}
        </div>
      </div>
    </div>
  );
});

TransactionRow.displayName = 'TransactionRow';

interface VirtualizedTransactionListProps {
  transactions: Transaction[];
  height?: number;
  itemHeight?: number;
}

const VirtualizedTransactionList: React.FC<VirtualizedTransactionListProps> = ({
  transactions,
  height = 400,
  itemHeight = 60
}) => {
  if (!transactions || transactions.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-white/40 mb-2">
          <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-white/10 flex items-center justify-center">
            📊
          </div>
        </div>
        <p className="text-white/60 text-lg mb-2">No transactions found</p>
        <p className="text-white/40 text-sm">Try adjusting your filters</p>
      </div>
    );
  }

  return (
    <div className="bg-white/5 rounded-xl overflow-hidden">
      {/* Table Header */}
      <div className="bg-white/10 px-4 py-3">
        <div className="grid grid-cols-6 gap-4 text-sm font-medium text-white/80">
          <div>Timestamp</div>
          <div>Type</div>
          <div>Description</div>
          <div>Amount</div>
          <div>Balance</div>
          <div>Status</div>
        </div>
      </div>
      
      {/* Virtualized List */}
      <List
        height={height}
        itemCount={transactions.length}
        itemSize={itemHeight}
        itemData={transactions}
        overscanCount={5}
      >
        {TransactionRow}
      </List>
    </div>
  );
};

export default memo(VirtualizedTransactionList);
