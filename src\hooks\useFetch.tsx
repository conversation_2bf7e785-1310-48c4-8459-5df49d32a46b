import { useState, useEffect, useCallback, useMemo } from 'react';
import { apiCache } from '../utils/apiCache';

interface UseFetchOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  body?: any;
  headers?: HeadersInit;
  enableCache?: boolean;
  cacheTime?: number;
}

const useFetch = (url: string, options?: UseFetchOptions) => {
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // Memoize options to prevent unnecessary re-renders
  const memoizedOptions = useMemo(() => options, [
    options?.method,
    options?.body,
    options?.headers,
    options?.enableCache,
    options?.cacheTime
  ]);

  const fetchData = useCallback(async (fetchUrl: string, fetchOptions?: UseFetchOptions) => {
    setLoading(true);
    setError(null);

    try {
      // Check cache for GET requests if caching is enabled
      if (fetchOptions?.enableCache !== false && (!fetchOptions?.method || fetchOptions.method === 'GET')) {
        const cached = apiCache.get(fetchUrl);
        if (cached) {
          setData(cached);
          setLoading(false);
          apiCache.trackHit();
          return;
        }
        apiCache.trackMiss();
      }

      const response = await fetch(fetchUrl, {
        method: fetchOptions?.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(fetchOptions?.headers || {}),
        },
        body: fetchOptions?.body ? JSON.stringify(fetchOptions.body) : undefined,
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      setData(result);

      // Cache GET responses if caching is enabled
      if (fetchOptions?.enableCache !== false && (!fetchOptions?.method || fetchOptions.method === 'GET')) {
        apiCache.set(fetchUrl, result, undefined, fetchOptions?.cacheTime);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch data when the URL or options change
  useEffect(() => {
    if (url) {
      fetchData(url, memoizedOptions);
    }
  }, [url, memoizedOptions, fetchData]);

  return { data, error, loading, fetchData };
};
export default useFetch;