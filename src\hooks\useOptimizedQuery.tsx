import { useQuery, UseQueryOptions, UseQueryResult } from '@tanstack/react-query';
import { useMemo, useCallback } from 'react';
import { apiCache } from '../utils/apiCache';

interface OptimizedQueryOptions<T> extends Omit<UseQueryOptions<T>, 'queryFn'> {
  url: string;
  params?: Record<string, any>;
  fetchFn?: (url: string, params?: Record<string, any>) => Promise<T>;
  cacheTime?: number;
}

/**
 * Enhanced useQuery hook with built-in caching and optimization
 */
export function useOptimizedQuery<T>({
  url,
  params,
  fetchFn,
  cacheTime,
  queryKey,
  ...options
}: OptimizedQueryOptions<T>): UseQueryResult<T> {
  
  // Memoize the query key to prevent unnecessary re-renders
  const memoizedQueryKey = useMemo(() => {
    if (queryKey) return queryKey;
    return [url, params];
  }, [url, params, queryKey]);

  // Memoized fetch function with caching
  const optimizedFetchFn = useCallback(async (): Promise<T> => {
    // Check cache first
    const cached = apiCache.get<T>(url, params);
    if (cached) {
      apiCache.trackHit();
      return cached;
    }

    apiCache.trackMiss();

    // Use custom fetch function or default fetch
    const data = fetchFn 
      ? await fetchFn(url, params)
      : await fetch(url).then(res => res.json());

    // Cache the result
    apiCache.set(url, data, params, cacheTime);

    return data;
  }, [url, params, fetchFn, cacheTime]);

  return useQuery<T>({
    queryKey: memoizedQueryKey,
    queryFn: optimizedFetchFn,
    staleTime: cacheTime || 1000 * 60 * 5, // 5 minutes default
    gcTime: (cacheTime || 1000 * 60 * 5) * 2, // Double the stale time
    ...options,
  });
}

/**
 * Hook for optimized infinite queries with virtual scrolling support
 */
export function useOptimizedInfiniteQuery<T>({
  url,
  params,
  fetchFn,
  cacheTime,
  queryKey,
  ...options
}: OptimizedQueryOptions<T> & {
  getNextPageParam?: (lastPage: T, pages: T[]) => any;
}) {
  const memoizedQueryKey = useMemo(() => {
    if (queryKey) return queryKey;
    return [url, 'infinite', params];
  }, [url, params, queryKey]);

  const optimizedFetchFn = useCallback(async ({ pageParam = 1 }): Promise<T> => {
    const pageParams = { ...params, page: pageParam };
    const pageUrl = `${url}?page=${pageParam}`;
    
    // Check cache for this specific page
    const cached = apiCache.get<T>(pageUrl, pageParams);
    if (cached) {
      apiCache.trackHit();
      return cached;
    }

    apiCache.trackMiss();

    const data = fetchFn 
      ? await fetchFn(pageUrl, pageParams)
      : await fetch(pageUrl).then(res => res.json());

    // Cache the page result
    apiCache.set(pageUrl, data, pageParams, cacheTime);

    return data;
  }, [url, params, fetchFn, cacheTime]);

  return useQuery({
    queryKey: memoizedQueryKey,
    queryFn: optimizedFetchFn,
    staleTime: cacheTime || 1000 * 60 * 5,
    gcTime: (cacheTime || 1000 * 60 * 5) * 2,
    ...options,
  });
}

/**
 * Hook to prefetch data for better UX
 */
export function usePrefetch() {
  const prefetch = useCallback((url: string, params?: Record<string, any>) => {
    // Check if already cached
    if (apiCache.get(url, params)) {
      return;
    }

    // Prefetch in background
    fetch(url)
      .then(res => res.json())
      .then(data => {
        apiCache.set(url, data, params);
      })
      .catch(error => {
        console.warn('Prefetch failed:', error);
      });
  }, []);

  return { prefetch };
}

/**
 * Hook to get cache statistics for debugging
 */
export function useCacheStats() {
  return useMemo(() => apiCache.getStats(), []);
}
