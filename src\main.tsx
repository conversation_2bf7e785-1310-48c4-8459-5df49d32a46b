// import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { AuthProvider } from './auth/AuthContext.tsx';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CoinsProvider } from './hooks/useCoinsQuery.tsx';
import { SocketProvider } from './context/socketProvider.tsx';

// Register service worker for aggressive caching
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Optimized QueryClient configuration for better performance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache data for 5 minutes by default
      staleTime: 1000 * 60 * 5,
      // Keep data in cache for 10 minutes
      gcTime: 1000 * 60 * 10,
      // Re<PERSON> failed requests only once
      retry: 1,
      // Don't refetch on window focus for better performance
      refetchOnWindowFocus: false,
      // Don't refetch on reconnect unless data is stale
      refetchOnReconnect: 'always',
      // Enable background refetching for fresh data
      refetchInterval: false,
    },
    mutations: {
      // Retry failed mutations once
      retry: 1,
    },
  },
});
createRoot(document.getElementById('root')!).render(
  // <StrictMode>
    <AuthProvider>
      <SocketProvider>
    <QueryClientProvider client={queryClient}>
      <CoinsProvider>
    <App />
    </CoinsProvider>
    </QueryClientProvider>
    </SocketProvider>
    </AuthProvider>
  // </StrictMode>
);
