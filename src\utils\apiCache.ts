interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface PendingRequest<T> {
  promise: Promise<T>;
  timestamp: number;
}

class ApiCache {
  private cache = new Map<string, CacheEntry<any>>();
  private pendingRequests = new Map<string, PendingRequest<any>>();
  private readonly defaultTTL = 5 * 60 * 1000; // 5 minutes
  private readonly maxCacheSize = 100;

  /**
   * Generate a cache key from URL and parameters
   */
  private generateKey(url: string, params?: Record<string, any>): string {
    const paramString = params ? JSON.stringify(params) : '';
    return `${url}:${paramString}`;
  }

  /**
   * Check if cache entry is still valid
   */
  private isValid(entry: CacheEntry<any>): boolean {
    return Date.now() < entry.expiresAt;
  }

  /**
   * Clean up expired entries and enforce cache size limit
   */
  private cleanup(): void {
    const now = Date.now();
    
    // Remove expired entries
    for (const [key, entry] of this.cache.entries()) {
      if (!this.isValid(entry)) {
        this.cache.delete(key);
      }
    }

    // Enforce cache size limit (LRU eviction)
    if (this.cache.size > this.maxCacheSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, entries.length - this.maxCacheSize);
      toRemove.forEach(([key]) => this.cache.delete(key));
    }

    // Clean up old pending requests (older than 30 seconds)
    for (const [key, request] of this.pendingRequests.entries()) {
      if (now - request.timestamp > 30000) {
        this.pendingRequests.delete(key);
      }
    }
  }

  /**
   * Get cached data if available and valid
   */
  get<T>(url: string, params?: Record<string, any>): T | null {
    const key = this.generateKey(url, params);
    const entry = this.cache.get(key);
    
    if (entry && this.isValid(entry)) {
      // Update timestamp for LRU
      entry.timestamp = Date.now();
      return entry.data;
    }
    
    return null;
  }

  /**
   * Set data in cache
   */
  set<T>(url: string, data: T, params?: Record<string, any>, ttl?: number): void {
    const key = this.generateKey(url, params);
    const now = Date.now();
    
    this.cache.set(key, {
      data,
      timestamp: now,
      expiresAt: now + (ttl || this.defaultTTL)
    });

    // Periodic cleanup
    if (Math.random() < 0.1) { // 10% chance
      this.cleanup();
    }
  }

  /**
   * Get or set pending request to prevent duplicate API calls
   */
  async getOrSetPending<T>(
    url: string, 
    requestFn: () => Promise<T>, 
    params?: Record<string, any>
  ): Promise<T> {
    const key = this.generateKey(url, params);
    
    // Check if request is already pending
    const pending = this.pendingRequests.get(key);
    if (pending) {
      return pending.promise;
    }

    // Create new request
    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now()
    });

    return promise;
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidate(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear();
    this.pendingRequests.clear();
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      cacheSize: this.cache.size,
      pendingRequests: this.pendingRequests.size,
      hitRate: this.calculateHitRate()
    };
  }

  private hitCount = 0;
  private missCount = 0;

  private calculateHitRate(): number {
    const total = this.hitCount + this.missCount;
    return total > 0 ? this.hitCount / total : 0;
  }

  /**
   * Track cache hit
   */
  trackHit(): void {
    this.hitCount++;
  }

  /**
   * Track cache miss
   */
  trackMiss(): void {
    this.missCount++;
  }
}

// Global cache instance
export const apiCache = new ApiCache();

/**
 * Enhanced fetch function with caching and deduplication
 */
export async function cachedFetch<T>(
  url: string,
  options?: RequestInit,
  params?: Record<string, any>,
  ttl?: number
): Promise<T> {
  // Only cache GET requests
  if (options?.method && options.method !== 'GET') {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Check cache first
  const cached = apiCache.get<T>(url, params);
  if (cached) {
    apiCache.trackHit();
    return cached;
  }

  apiCache.trackMiss();

  // Use request deduplication
  return apiCache.getOrSetPending(url, async () => {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Cache the response
    apiCache.set(url, data, params, ttl);
    
    return data;
  }, params);
}

export default apiCache;
