/**
 * Bundle optimization utilities
 */

// Dynamic imports for heavy libraries
export const loadHeavyLibraries = {
  // Load Konva only when needed
  konva: () => import('konva'),
  reactKonva: () => import('react-konva'),
  
  // Load chart libraries only when needed
  charts: () => import('react-chartjs-2'),
  
  // Load animation libraries only when needed
  animations: () => import('@react-spring/web'),
  
  // Load form libraries only when needed
  formik: () => import('formik'),
  yup: () => import('yup'),
  
  // Load date libraries only when needed
  dayjs: () => import('dayjs'),
  
  // Load carousel only when needed
  embla: () => import('embla-carousel-react'),
};

/**
 * Lazy load component with error boundary
 */
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) {
  const LazyComponent = React.lazy(importFn);
  
  return React.forwardRef<any, React.ComponentProps<T>>((props, ref) => (
    <React.Suspense 
      fallback={
        fallback ? 
        React.createElement(fallback) : 
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      }
    >
      <LazyComponent {...props} ref={ref} />
    </React.Suspense>
  ));
}

/**
 * Tree-shakable icon imports
 */
export const Icons = {
  // Only import icons that are actually used
  ChevronLeft: () => import('lucide-react').then(mod => ({ default: mod.ChevronLeft })),
  ChevronRight: () => import('lucide-react').then(mod => ({ default: mod.ChevronRight })),
  Home: () => import('lucide-react').then(mod => ({ default: mod.Home })),
  User: () => import('lucide-react').then(mod => ({ default: mod.User })),
  Settings: () => import('lucide-react').then(mod => ({ default: mod.Settings })),
  Search: () => import('lucide-react').then(mod => ({ default: mod.Search })),
  Filter: () => import('lucide-react').then(mod => ({ default: mod.Filter })),
  Menu: () => import('lucide-react').then(mod => ({ default: mod.Menu })),
  X: () => import('lucide-react').then(mod => ({ default: mod.X })),
  Play: () => import('lucide-react').then(mod => ({ default: mod.Play })),
  Pause: () => import('lucide-react').then(mod => ({ default: mod.Pause })),
  Volume2: () => import('lucide-react').then(mod => ({ default: mod.Volume2 })),
  VolumeX: () => import('lucide-react').then(mod => ({ default: mod.VolumeX })),
};

/**
 * Optimized icon component
 */
export function OptimizedIcon({ 
  name, 
  size = 24, 
  className = '',
  ...props 
}: {
  name: keyof typeof Icons;
  size?: number;
  className?: string;
  [key: string]: any;
}) {
  const [IconComponent, setIconComponent] = React.useState<React.ComponentType<any> | null>(null);
  
  React.useEffect(() => {
    Icons[name]().then(({ default: Icon }) => {
      setIconComponent(() => Icon);
    });
  }, [name]);
  
  if (!IconComponent) {
    return (
      <div 
        className={`inline-block bg-gray-300 rounded ${className}`}
        style={{ width: size, height: size }}
      />
    );
  }
  
  return <IconComponent size={size} className={className} {...props} />;
}

/**
 * Bundle analyzer helper
 */
export function analyzeBundleSize() {
  if (process.env.NODE_ENV === 'development') {
    // Analyze current bundle composition
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
    
    console.group('📦 Bundle Analysis');
    console.log('Scripts loaded:', scripts.length);
    console.log('Stylesheets loaded:', styles.length);
    
    // Estimate sizes (rough approximation)
    scripts.forEach((script: any) => {
      if (script.src.includes('chunk')) {
        console.log(`Script: ${script.src.split('/').pop()}`);
      }
    });
    
    console.groupEnd();
  }
}

/**
 * Preload critical chunks
 */
export function preloadCriticalChunks() {
  const criticalChunks = [
    '/assets/vendor',
    '/assets/router',
    '/assets/ui'
  ];
  
  criticalChunks.forEach(chunk => {
    const link = document.createElement('link');
    link.rel = 'modulepreload';
    link.href = chunk;
    document.head.appendChild(link);
  });
}

/**
 * Remove unused CSS
 */
export function removeUnusedCSS() {
  if (process.env.NODE_ENV === 'production') {
    // This would typically be done at build time with PurgeCSS
    // But we can remove some runtime unused styles
    const unusedSelectors = [
      '.unused-class',
      '.debug-only',
      '.development-only'
    ];
    
    unusedSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });
  }
}

/**
 * Optimize third-party scripts
 */
export function optimizeThirdPartyScripts() {
  // Defer non-critical third-party scripts
  const thirdPartyScripts = [
    'analytics',
    'chat-widget',
    'social-media'
  ];
  
  thirdPartyScripts.forEach(scriptType => {
    // Load after main content is ready
    setTimeout(() => {
      loadThirdPartyScript(scriptType);
    }, 2000);
  });
}

function loadThirdPartyScript(type: string) {
  // Implementation would depend on specific third-party services
  console.log(`Loading ${type} script...`);
}

/**
 * Memory optimization
 */
export function optimizeMemoryUsage() {
  // Clean up event listeners
  const cleanupFunctions: (() => void)[] = [];
  
  // Debounce resize events
  let resizeTimeout: NodeJS.Timeout;
  const debouncedResize = () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      window.dispatchEvent(new Event('optimized-resize'));
    }, 250);
  };
  
  window.addEventListener('resize', debouncedResize);
  cleanupFunctions.push(() => {
    window.removeEventListener('resize', debouncedResize);
  });
  
  // Clean up on page unload
  window.addEventListener('beforeunload', () => {
    cleanupFunctions.forEach(cleanup => cleanup());
  });
  
  return () => {
    cleanupFunctions.forEach(cleanup => cleanup());
  };
}

/**
 * Code splitting helper
 */
export function createCodeSplitRoute(
  importFn: () => Promise<{ default: React.ComponentType<any> }>,
  preload = false
) {
  const LazyComponent = React.lazy(importFn);
  
  if (preload) {
    // Preload the component
    importFn();
  }
  
  return LazyComponent;
}

/**
 * Performance monitoring for bundle optimization
 */
export function monitorBundlePerformance() {
  if ('performance' in window) {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const entries = performance.getEntriesByType('resource');
        const jsEntries = entries.filter(entry => 
          entry.name.endsWith('.js') && entry.name.includes('assets')
        );
        
        console.group('📊 Bundle Performance');
        console.log(`Total JS files: ${jsEntries.length}`);
        
        const totalSize = jsEntries.reduce((sum, entry) => {
          return sum + (entry.transferSize || 0);
        }, 0);
        
        console.log(`Total JS size: ${(totalSize / 1024).toFixed(2)} KB`);
        console.groupEnd();
      }, 1000);
    });
  }
}

// Initialize optimizations
export function initializeBundleOptimizations() {
  analyzeBundleSize();
  preloadCriticalChunks();
  removeUnusedCSS();
  optimizeThirdPartyScripts();
  monitorBundlePerformance();
  
  return optimizeMemoryUsage();
}
