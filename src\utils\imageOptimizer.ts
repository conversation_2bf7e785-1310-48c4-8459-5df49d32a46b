interface ImageOptimizationOptions {
  quality?: number;
  format?: 'webp' | 'avif' | 'jpeg' | 'png';
  width?: number;
  height?: number;
  lazy?: boolean;
  priority?: boolean;
}

class ImageOptimizer {
  private cache = new Map<string, string>();
  private observer?: IntersectionObserver;
  private loadingImages = new Set<string>();

  constructor() {
    this.setupIntersectionObserver();
  }

  /**
   * Setup intersection observer for lazy loading
   */
  private setupIntersectionObserver() {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return;
    }

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            this.loadImage(img);
            this.observer?.unobserve(img);
          }
        });
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
      }
    );
  }

  /**
   * Optimize image URL with parameters
   */
  optimizeImageUrl(src: string, options: ImageOptimizationOptions = {}): string {
    const {
      quality = 80,
      format = 'webp',
      width,
      height,
    } = options;

    // If already optimized, return cached version
    const cacheKey = `${src}-${quality}-${format}-${width}-${height}`;
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // For local images, try to find optimized versions
    if (src.startsWith('/src/assets/') || src.startsWith('./src/assets/')) {
      const optimizedSrc = this.getOptimizedLocalImage(src, format);
      this.cache.set(cacheKey, optimizedSrc);
      return optimizedSrc;
    }

    // For external images, add optimization parameters
    const url = new URL(src, window.location.origin);
    
    if (quality !== 80) {
      url.searchParams.set('q', quality.toString());
    }
    
    if (width) {
      url.searchParams.set('w', width.toString());
    }
    
    if (height) {
      url.searchParams.set('h', height.toString());
    }
    
    if (format !== 'webp') {
      url.searchParams.set('f', format);
    }

    const optimizedSrc = url.toString();
    this.cache.set(cacheKey, optimizedSrc);
    return optimizedSrc;
  }

  /**
   * Get optimized local image path
   */
  private getOptimizedLocalImage(src: string, format: string): string {
    // Try to find format-specific version
    const basePath = src.replace(/\.(jpg|jpeg|png|webp|avif)$/i, '');
    
    // Priority order: avif > webp > original
    const formats = ['avif', 'webp'];
    
    for (const fmt of formats) {
      if (fmt === format || format === 'auto') {
        const optimizedPath = `${basePath}.${fmt}`;
        // In a real implementation, you'd check if the file exists
        // For now, we'll assume avif/webp versions exist
        if (this.imageExists(optimizedPath)) {
          return optimizedPath;
        }
      }
    }

    return src; // Fallback to original
  }

  /**
   * Check if image exists (simplified)
   */
  private imageExists(src: string): boolean {
    // In a real implementation, you might:
    // 1. Check a manifest of available images
    // 2. Make a HEAD request
    // 3. Use a build-time generated list
    
    // For now, assume avif/webp versions exist for common images
    return src.includes('.avif') || src.includes('.webp');
  }

  /**
   * Preload critical images
   */
  preloadImages(images: string[], options: ImageOptimizationOptions = {}) {
    images.forEach(src => {
      const optimizedSrc = this.optimizeImageUrl(src, options);
      
      if (!this.loadingImages.has(optimizedSrc)) {
        this.loadingImages.add(optimizedSrc);
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = optimizedSrc;
        
        // Add responsive image hints
        if (options.width && options.height) {
          link.setAttribute('imagesizes', `${options.width}px`);
        }
        
        document.head.appendChild(link);
      }
    });
  }

  /**
   * Load image with optimization
   */
  private loadImage(img: HTMLImageElement) {
    const src = img.dataset.src || img.src;
    if (!src || this.loadingImages.has(src)) return;

    this.loadingImages.add(src);

    // Create optimized image
    const optimizedImg = new Image();
    
    optimizedImg.onload = () => {
      img.src = optimizedImg.src;
      img.classList.remove('lazy-loading');
      img.classList.add('lazy-loaded');
      this.loadingImages.delete(src);
    };

    optimizedImg.onerror = () => {
      // Fallback to original source
      img.src = src;
      img.classList.remove('lazy-loading');
      this.loadingImages.delete(src);
    };

    // Try to load optimized version
    const optimizedSrc = this.optimizeImageUrl(src, {
      format: 'webp',
      quality: 80
    });

    optimizedImg.src = optimizedSrc;
  }

  /**
   * Setup lazy loading for an image element
   */
  setupLazyLoading(img: HTMLImageElement, options: ImageOptimizationOptions = {}) {
    if (!this.observer) {
      // Fallback for browsers without IntersectionObserver
      this.loadImage(img);
      return;
    }

    if (options.priority) {
      // Load immediately for priority images
      this.loadImage(img);
    } else {
      // Add to lazy loading queue
      img.classList.add('lazy-loading');
      this.observer.observe(img);
    }
  }

  /**
   * Generate responsive image srcset
   */
  generateSrcSet(src: string, widths: number[] = [320, 640, 1024, 1280, 1920]): string {
    return widths
      .map(width => {
        const optimizedSrc = this.optimizeImageUrl(src, { width, format: 'webp' });
        return `${optimizedSrc} ${width}w`;
      })
      .join(', ');
  }

  /**
   * Create picture element with multiple formats
   */
  createPictureElement(src: string, alt: string, options: ImageOptimizationOptions = {}): HTMLPictureElement {
    const picture = document.createElement('picture');
    
    // Add AVIF source
    const avifSource = document.createElement('source');
    avifSource.srcset = this.optimizeImageUrl(src, { ...options, format: 'avif' });
    avifSource.type = 'image/avif';
    picture.appendChild(avifSource);
    
    // Add WebP source
    const webpSource = document.createElement('source');
    webpSource.srcset = this.optimizeImageUrl(src, { ...options, format: 'webp' });
    webpSource.type = 'image/webp';
    picture.appendChild(webpSource);
    
    // Add fallback img
    const img = document.createElement('img');
    img.src = this.optimizeImageUrl(src, options);
    img.alt = alt;
    img.loading = options.lazy ? 'lazy' : 'eager';
    
    if (options.width) img.width = options.width;
    if (options.height) img.height = options.height;
    
    picture.appendChild(img);
    
    return picture;
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.observer?.disconnect();
    this.cache.clear();
    this.loadingImages.clear();
  }

  /**
   * Get optimization statistics
   */
  getStats() {
    return {
      cacheSize: this.cache.size,
      loadingImages: this.loadingImages.size,
      observerActive: !!this.observer
    };
  }
}

// Global image optimizer instance
export const imageOptimizer = new ImageOptimizer();

/**
 * Utility function to optimize image loading
 */
export function optimizeImage(
  src: string, 
  options: ImageOptimizationOptions = {}
): string {
  return imageOptimizer.optimizeImageUrl(src, options);
}

/**
 * Preload critical images
 */
export function preloadCriticalImages(images: string[]) {
  imageOptimizer.preloadImages(images, { priority: true, format: 'webp' });
}

/**
 * Setup lazy loading for images
 */
export function setupImageLazyLoading() {
  // Find all images with data-src attribute
  const lazyImages = document.querySelectorAll('img[data-src]');
  
  lazyImages.forEach(img => {
    imageOptimizer.setupLazyLoading(img as HTMLImageElement);
  });
}

export default ImageOptimizer;
