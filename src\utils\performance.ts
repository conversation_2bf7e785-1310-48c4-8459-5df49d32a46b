import { apiCache } from './apiCache';
import { getPreloadStats } from './preloader';

interface PerformanceMetrics {
  navigation: {
    domContentLoaded: number;
    pageLoad: number;
    totalLoadTime: number;
    firstContentfulPaint?: number;
    largestContentfulPaint?: number;
  };
  cache: {
    hitRate: number;
    cacheSize: number;
    pendingRequests: number;
  };
  preloading: {
    routesPreloaded: number;
    totalRoutes: number;
    preloadPercentage: number;
  };
  memory?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    // Observe navigation timing
    if ('PerformanceObserver' in window) {
      try {
        const navObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.entryType === 'navigation') {
              this.updateNavigationMetrics(entry as PerformanceNavigationTiming);
            }
          });
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navObserver);

        // Observe paint timing
        const paintObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.navigation = {
                ...this.metrics.navigation,
                firstContentfulPaint: entry.startTime
              } as any;
            }
          });
        });
        paintObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(paintObserver);

        // Observe LCP
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          if (lastEntry) {
            this.metrics.navigation = {
              ...this.metrics.navigation,
              largestContentfulPaint: lastEntry.startTime
            } as any;
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (error) {
        console.warn('Performance observers not supported:', error);
      }
    }
  }

  private updateNavigationMetrics(perfData: PerformanceNavigationTiming) {
    this.metrics.navigation = {
      domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
      pageLoad: perfData.loadEventEnd - perfData.loadEventStart,
      totalLoadTime: perfData.loadEventEnd - perfData.fetchStart,
      ...this.metrics.navigation
    };
  }

  private updateCacheMetrics() {
    const stats = apiCache.getStats();
    this.metrics.cache = {
      hitRate: Math.round(stats.hitRate * 100),
      cacheSize: stats.cacheSize,
      pendingRequests: stats.pendingRequests
    };
  }

  private updatePreloadingMetrics() {
    const stats = getPreloadStats();
    this.metrics.preloading = {
      routesPreloaded: stats.preloaded,
      totalRoutes: stats.total,
      preloadPercentage: stats.percentage
    };
  }

  private updateMemoryMetrics() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.memory = {
        usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
        totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
        jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) // MB
      };
    }
  }

  public getMetrics(): PerformanceMetrics {
    this.updateCacheMetrics();
    this.updatePreloadingMetrics();
    this.updateMemoryMetrics();

    return this.metrics as PerformanceMetrics;
  }

  public logMetrics() {
    const metrics = this.getMetrics();

    console.group('🚀 Performance Metrics');

    if (metrics.navigation) {
      console.group('📊 Navigation Timing');
      console.log(`DOM Content Loaded: ${metrics.navigation.domContentLoaded}ms`);
      console.log(`Page Load: ${metrics.navigation.pageLoad}ms`);
      console.log(`Total Load Time: ${metrics.navigation.totalLoadTime}ms`);
      if (metrics.navigation.firstContentfulPaint) {
        console.log(`First Contentful Paint: ${Math.round(metrics.navigation.firstContentfulPaint)}ms`);
      }
      if (metrics.navigation.largestContentfulPaint) {
        console.log(`Largest Contentful Paint: ${Math.round(metrics.navigation.largestContentfulPaint)}ms`);
      }
      console.groupEnd();
    }

    if (metrics.cache) {
      console.group('💾 Cache Performance');
      console.log(`Cache Hit Rate: ${metrics.cache.hitRate}%`);
      console.log(`Cache Size: ${metrics.cache.cacheSize} entries`);
      console.log(`Pending Requests: ${metrics.cache.pendingRequests}`);
      console.groupEnd();
    }

    if (metrics.preloading) {
      console.group('⚡ Route Preloading');
      console.log(`Routes Preloaded: ${metrics.preloading.routesPreloaded}/${metrics.preloading.totalRoutes} (${metrics.preloading.preloadPercentage}%)`);
      console.groupEnd();
    }

    if (metrics.memory) {
      console.group('🧠 Memory Usage');
      console.log(`Used Heap: ${metrics.memory.usedJSHeapSize}MB`);
      console.log(`Total Heap: ${metrics.memory.totalJSHeapSize}MB`);
      console.log(`Heap Limit: ${metrics.memory.jsHeapSizeLimit}MB`);
      console.groupEnd();
    }

    console.groupEnd();
  }

  public cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

export const measurePerformance = () => {
  if (typeof window !== 'undefined' && 'performance' in window) {
    window.addEventListener('load', () => {
      setTimeout(() => {
        performanceMonitor.logMetrics();
      }, 1000);
    });

    // Log metrics periodically in development
    if (process.env.NODE_ENV === 'development') {
      setInterval(() => {
        performanceMonitor.logMetrics();
      }, 30000); // Every 30 seconds
    }
  }
};

// Export for use in components
export const getPerformanceMetrics = () => performanceMonitor.getMetrics();

// Call in main.tsx
measurePerformance();