interface PreloadedRoute {
  component: () => Promise<any>;
  preloaded: boolean;
  priority: number;
}

// Route preloading registry
const routeRegistry = new Map<string, PreloadedRoute>();

// Register routes with priorities
const registerRoute = (path: string, component: () => Promise<any>, priority: number = 1) => {
  routeRegistry.set(path, {
    component,
    preloaded: false,
    priority
  });
};

// Initialize route registry
const initializeRoutes = () => {
  // Critical routes (priority 1 - preload immediately)
  registerRoute('/home', () => import('../pages/Home'), 1);
  registerRoute('/auth', () => import('../pages/Auth'), 1);

  // High priority routes (priority 2 - preload after critical)
  registerRoute('/wallet', () => import('../pages/Wallet'), 2);
  registerRoute('/map', () => import('../pages/Map'), 2);

  // Medium priority routes (priority 3 - preload on idle)
  registerRoute('/shop', () => import('../pages/Shop'), 3);
  registerRoute('/rewards', () => import('../pages/Rewards'), 3);
  registerRoute('/leaderboard', () => import('../pages/Leaderboard'), 3);

  // Low priority routes (priority 4 - preload on demand)
  registerRoute('/squads', () => import('../pages/Squads'), 4);
  registerRoute('/lottery', () => import('../pages/Lottery'), 4);
};

// Preload critical resources
export const preloadCriticalResources = () => {
  // Initialize routes
  initializeRoutes();

  // Preload hero images
  const heroImages = [
    '/src/assets/images/logo-white.avif',
    '/src/assets/images/background.avif'
  ];

  heroImages.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });

  // Preload critical routes (priority 1)
  preloadRoutesByPriority(1);
};

// Preload routes by priority level
const preloadRoutesByPriority = (priority: number) => {
  for (const [path, route] of routeRegistry.entries()) {
    if (route.priority === priority && !route.preloaded) {
      route.component().then(() => {
        route.preloaded = true;
        console.log(`Route ${path} preloaded`);
      }).catch(error => {
        console.warn(`Failed to preload route ${path}:`, error);
      });
    }
  }
};

// Enhanced prefetch with intelligent timing
export const prefetchResources = () => {
  // Preload high priority routes after initial load
  setTimeout(() => {
    preloadRoutesByPriority(2);
  }, 1000);

  // Preload medium priority routes when browser is idle
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      preloadRoutesByPriority(3);
    }, { timeout: 5000 });
  } else {
    setTimeout(() => {
      preloadRoutesByPriority(3);
    }, 3000);
  }

  // Preload low priority routes on user interaction
  const preloadLowPriority = () => {
    preloadRoutesByPriority(4);
    // Remove listeners after first interaction
    document.removeEventListener('mouseover', preloadLowPriority);
    document.removeEventListener('touchstart', preloadLowPriority);
  };

  document.addEventListener('mouseover', preloadLowPriority, { once: true });
  document.addEventListener('touchstart', preloadLowPriority, { once: true });
};

// Preload specific route on demand
export const preloadRoute = (path: string) => {
  const route = routeRegistry.get(path);
  if (route && !route.preloaded) {
    route.component().then(() => {
      route.preloaded = true;
      console.log(`Route ${path} preloaded on demand`);
    });
  }
};

// Get preloading statistics
export const getPreloadStats = () => {
  const total = routeRegistry.size;
  const preloaded = Array.from(routeRegistry.values()).filter(r => r.preloaded).length;
  return {
    total,
    preloaded,
    percentage: total > 0 ? Math.round((preloaded / total) * 100) : 0
  };
};