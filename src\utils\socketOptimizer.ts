import { Socket } from 'socket.io-client';
import { useEffect } from 'react';

interface SocketEvent {
  event: string;
  data: any;
  timestamp: number;
}

interface ThrottledEvent {
  lastEmit: number;
  interval: number;
  pending?: any;
  timeoutId?: NodeJS.Timeout;
}

class SocketOptimizer {
  private socket: Socket | null = null;
  private eventQueue: SocketEvent[] = [];
  private throttledEvents = new Map<string, ThrottledEvent>();
  private batchedEvents = new Map<string, any[]>();
  private isProcessing = false;
  private maxQueueSize = 100;
  private batchInterval = 50; // 50ms batching
  private batchTimeoutId?: NodeJS.Timeout;

  constructor(socket: Socket) {
    this.socket = socket;
    this.setupEventThrottling();
    this.startBatchProcessor();
  }

  /**
   * Setup throttling for specific events
   */
  private setupEventThrottling() {
    // Configure throttling for different event types
    this.throttledEvents.set('balance_update', {
      lastEmit: 0,
      interval: 1000 // 1 second
    });

    this.throttledEvents.set('game_state', {
      lastEmit: 0,
      interval: 100 // 100ms
    });

    this.throttledEvents.set('user_activity', {
      lastEmit: 0,
      interval: 5000 // 5 seconds
    });
  }

  /**
   * Optimized emit with throttling and batching
   */
  emit(event: string, data: any) {
    if (!this.socket) return;

    const now = Date.now();
    const throttleConfig = this.throttledEvents.get(event);

    if (throttleConfig) {
      this.handleThrottledEvent(event, data, now, throttleConfig);
    } else if (this.shouldBatch(event)) {
      this.addToBatch(event, data);
    } else {
      this.socket.emit(event, data);
    }
  }

  /**
   * Handle throttled events
   */
  private handleThrottledEvent(
    event: string,
    data: any,
    now: number,
    config: ThrottledEvent
  ) {
    if (now - config.lastEmit >= config.interval) {
      // Emit immediately
      this.socket?.emit(event, data);
      config.lastEmit = now;
    } else {
      // Queue for later emission
      config.pending = data;
      
      if (config.timeoutId) {
        clearTimeout(config.timeoutId);
      }

      config.timeoutId = setTimeout(() => {
        if (config.pending && this.socket) {
          this.socket.emit(event, config.pending);
          config.lastEmit = Date.now();
          config.pending = undefined;
        }
      }, config.interval - (now - config.lastEmit));
    }
  }

  /**
   * Check if event should be batched
   */
  private shouldBatch(event: string): boolean {
    return event.includes('batch_') || 
           event === 'analytics' || 
           event === 'metrics';
  }

  /**
   * Add event to batch
   */
  private addToBatch(event: string, data: any) {
    if (!this.batchedEvents.has(event)) {
      this.batchedEvents.set(event, []);
    }
    
    const batch = this.batchedEvents.get(event)!;
    batch.push(data);

    // Limit batch size
    if (batch.length > 10) {
      this.flushBatch(event);
    }
  }

  /**
   * Start batch processor
   */
  private startBatchProcessor() {
    this.batchTimeoutId = setInterval(() => {
      this.flushAllBatches();
    }, this.batchInterval);
  }

  /**
   * Flush specific batch
   */
  private flushBatch(event: string) {
    const batch = this.batchedEvents.get(event);
    if (batch && batch.length > 0 && this.socket) {
      this.socket.emit(`${event}_batch`, batch);
      this.batchedEvents.set(event, []);
    }
  }

  /**
   * Flush all batches
   */
  private flushAllBatches() {
    for (const event of this.batchedEvents.keys()) {
      this.flushBatch(event);
    }
  }

  /**
   * Optimized listener with debouncing
   */
  on(event: string, callback: (...args: any[]) => void, debounceMs = 0) {
    if (!this.socket) return;

    if (debounceMs > 0) {
      let timeoutId: NodeJS.Timeout;
      
      this.socket.on(event, (...args) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          callback(...args);
        }, debounceMs);
      });
    } else {
      this.socket.on(event, callback);
    }
  }

  /**
   * Connection health monitoring
   */
  monitorConnection() {
    if (!this.socket) return;

    let pingInterval: NodeJS.Timeout;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;

    this.socket.on('connect', () => {
      console.log('Socket connected');
      reconnectAttempts = 0;
      
      // Start ping monitoring
      pingInterval = setInterval(() => {
        if (this.socket?.connected) {
          this.socket.emit('ping', Date.now());
        }
      }, 30000); // Ping every 30 seconds
    });

    this.socket.on('disconnect', () => {
      console.log('Socket disconnected');
      clearInterval(pingInterval);
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      reconnectAttempts++;
      
      if (reconnectAttempts >= maxReconnectAttempts) {
        console.error('Max reconnection attempts reached');
        this.socket?.disconnect();
      }
    });

    this.socket.on('pong', (timestamp) => {
      const latency = Date.now() - timestamp;
      console.log(`Socket latency: ${latency}ms`);
    });
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    // Clear all timeouts
    for (const config of this.throttledEvents.values()) {
      if (config.timeoutId) {
        clearTimeout(config.timeoutId);
      }
    }

    if (this.batchTimeoutId) {
      clearInterval(this.batchTimeoutId);
    }

    // Flush remaining batches
    this.flushAllBatches();

    // Clear maps
    this.throttledEvents.clear();
    this.batchedEvents.clear();
    this.eventQueue = [];
  }

  /**
   * Get optimization statistics
   */
  getStats() {
    return {
      queueSize: this.eventQueue.length,
      throttledEvents: this.throttledEvents.size,
      batchedEvents: Array.from(this.batchedEvents.entries()).map(([event, batch]) => ({
        event,
        batchSize: batch.length
      })),
      isConnected: this.socket?.connected || false
    };
  }
}

/**
 * Factory function to create optimized socket
 */
export function createOptimizedSocket(socket: Socket): SocketOptimizer {
  const optimizer = new SocketOptimizer(socket);
  optimizer.monitorConnection();
  return optimizer;
}

/**
 * Hook for using optimized socket in React components
 */
export function useOptimizedSocket(socket: Socket | null) {
  if (!socket) return null;

  const optimizer = new SocketOptimizer(socket);

  // Cleanup on unmount
  useEffect(() => {
    return () => optimizer.cleanup();
  }, [optimizer]);

  return optimizer;
}

export default SocketOptimizer;
