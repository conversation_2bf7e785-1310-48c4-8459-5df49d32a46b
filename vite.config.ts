import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Core React libraries
          vendor: ['react', 'react-dom'],

          // Routing and navigation
          router: ['react-router-dom'],

          // UI and styling libraries
          ui: ['lucide-react', '@react-spring/web'],

          // Data fetching and state management
          query: ['@tanstack/react-query', 'axios'],

          // Form handling
          forms: ['formik', 'yup'],

          // Game components (lazy loaded)
          games: [
            './src/pages/CrashGame.tsx',
            './src/pages/CrashGame2.tsx',
            './src/pages/CryptoKing.tsx',
            './src/pages/PlinkoGame.tsx'
          ],

          // Utility libraries
          utils: ['dayjs', 'react-hot-toast'],

          // Canvas and graphics
          canvas: ['konva', 'react-konva', 'use-image'],

          // Real-time features
          realtime: ['socket.io-client']
        }
      }
    },
    cssCodeSplit: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2
      },
      mangle: {
        safari10: true
      }
    },
    // Optimize chunk sizes
    chunkSizeWarningLimit: 1000,
    // Enable source maps for production debugging (optional)
    sourcemap: false,
    // Optimize asset handling
    assetsInlineLimit: 4096,
    // Enable CSS minification
    cssMinify: true
  },
  server: {
    hmr: {
      overlay: false
    }
  },
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      'axios',
      'lucide-react'
    ],
    exclude: [
      // Exclude large libraries that should be lazy loaded
      'konva',
      'react-konva'
    ]
  },
  // Enable experimental features for better performance
  esbuild: {
    // Remove console logs in production
    drop: ['console', 'debugger'],
    // Enable tree shaking
    treeShaking: true
  }
})